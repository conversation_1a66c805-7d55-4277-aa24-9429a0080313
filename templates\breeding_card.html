<div class="breeding-card{% if horse.has_gold_border %} gold-border{% endif %}"
     data-status="{{ horse.status|lower }}"
     data-gender="{{ horse.gender|lower }}"
     data-pregnant="{{ horse.pregnant|default('false')|lower }}"
     data-age="{{ horse.age }}"
     data-total-stars="{{ horse.total_stars }}"
     data-direction="{{ horse.direction|lower }}"
     data-surface="{{ horse.surface|lower }}"
     data-condition="{{ horse.condition|lower }}">
    
    <!-- Horse Header with Grade and Image -->
    <div class="breeding-header">
        <div class="breeding-info">
            <div class="grade-badge">{{ horse.grade }}</div>
            <h2 class="horse-name">{{ horse.name }}</h2>
            <div class="horse-details">
                <span class="detail-item">Grade: {{ horse.grade }}</span>
                <span class="detail-separator">|</span>
                <span class="detail-item">{{ horse.gender }}</span>
                <span class="detail-separator">|</span>
                <span class="detail-item">Age: {{ horse.age }}</span>
            </div>
        </div>
        <div class="horse-image-container">
            <img src="{{ horse.image_url }}" alt="{{ horse.name }}" class="horse-image" />
        </div>
    </div>

    <!-- Preferences Row -->
    <div class="preferences-row">
        <div class="preference-item">
            <span class="preference-label">{{ horse.direction }}</span>
            <span class="preference-stars">{{ horse.direction_stars }}</span>
        </div>
        <div class="preference-item">
            <span class="preference-label">{{ horse.surface }}</span>
            <span class="preference-stars">{{ horse.surface_stars }}</span>
        </div>
        <div class="preference-item">
            <span class="preference-label">{{ horse.condition }}</span>
            <span class="preference-stars">{{ horse.condition_stars }}</span>
        </div>
    </div>

    <!-- Racing Stats Row -->
    <div class="racing-stats-row">
        <div class="stat-number">{{ horse.wins|default('0') }}</div>
        <div class="stat-record">({{ horse.wins|default('0') }} - {{ horse.places|default('0') }} - {{ horse.shows|default('0') }} - {{ horse.starts|default('0') }})</div>
        <div class="win-percentage">{{ horse.win_percentage|default('0') }}%</div>
        <div class="earnings">{{ horse.total_earnings|default('0') }}</div>
        <div class="currency-icon">🪙</div>
    </div>

    <!-- Grade Badges Row -->
    <div class="grade-badges-row">
        <div class="grade-badge-item">
            <div class="grade-badge-circle start-grade">{{ horse.racing_start|default('--') }}</div>
            <span class="grade-label">Start</span>
        </div>
        <div class="grade-badge-item">
            <div class="grade-badge-circle speed-grade">{{ horse.racing_speed|default('--') }}</div>
            <span class="grade-label">Speed</span>
        </div>
        <div class="grade-badge-item">
            <div class="grade-badge-circle stamina-grade">{{ horse.racing_stamina|default('--') }}</div>
            <span class="grade-label">Stamina</span>
        </div>
        <div class="grade-badge-item">
            <div class="grade-badge-circle finish-grade">{{ horse.racing_finish|default('--') }}</div>
            <span class="grade-label">Finish</span>
        </div>
        <div class="grade-badge-item">
            <div class="grade-badge-circle heart-grade">{{ horse.racing_heart|default('--') }}</div>
            <span class="grade-label">Heart</span>
        </div>
        <div class="grade-badge-item">
            <div class="grade-badge-circle temper-grade">{{ horse.racing_temper|default('--') }}</div>
            <span class="grade-label">Temper</span>
        </div>
        <div class="grade-badge-item">
            <div class="grade-badge-circle subgrade">3</div>
            <span class="grade-label">SubGrade</span>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="breeding-card-buttons">
        <button class="btn btn-primary breeding-btn" onclick="viewRacingProfile('{{ horse.name }}')">
            View Racing Profile
        </button>
        <button class="btn btn-secondary breeding-btn" onclick="viewBreedingProfile('{{ horse.name }}')">
            View Breeding Profile
        </button>
    </div>
</div>
