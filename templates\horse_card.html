<div class="horse-card"
     data-status="{{ horse.status|lower }}"
     data-gender="{{ horse.type|lower }}"
     data-pregnant="{{ horse.pregnant|default('false')|lower }}"
     data-age="{{ horse.age }}">
  <div class="horse-card-header">
    <h2>{{ horse.name }}</h2>
    <img src="{{ horse.image_url }}" alt="{{ horse.name }}" class="horse-image">
  </div>
  <div class="horse-card-details">
    <p>Grade: {{ horse.grade }} | {{ horse.type }} | Age: {{ horse.age }}  | Gender: {{ horse.gender }}</p>
    <p>
      {{ horse.direction }}  {{ horse.direction_stars }}  
      {{ horse.surface }}  {{ horse.surface_stars }}
      {{ horse.condition }}  {{ horse.condition_stars }}
    </p>
    <div class="horse-status {{ horse.status|lower }}">
      {{ horse.status }}
    </div>
    <div class="horse-card-buttons">
        <button class="horse-card-buttons">View Racing Profile</button>
        <button class="horse-card-buttons">View Breeding Profile</button>
    </div>
  </div>
</div>