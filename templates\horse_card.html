<div class="horse-card{% if horse.has_gold_border %} gold-border{% endif %}"
     data-status="{{ horse.status|lower }}"
     data-gender="{{ horse.gender|lower }}"
     data-pregnant="{{ horse.pregnant|default('false')|lower }}"
     data-age="{{ horse.age }}"
     data-total-stars="{{ horse.total_stars }}"
     data-direction="{{ horse.direction|lower }}"
     data-surface="{{ horse.surface|lower }}"
     data-condition="{{ horse.condition|lower }}">
  <div class="horse-card-header">
    <h2>{{ horse.name }}</h2>
    <img src="{{ horse.image_url }}" alt="{{ horse.name }}" class="horse-image">
  </div>
  <div class="horse-card-details">
    <p>Grade: {{ horse.grade }} | Gender: {{ horse.gender }} | Age: {{ horse.age }}</p>
    <p>
      <span class="stat-item">{{ horse.direction }} <span class="stars">{{ horse.direction_stars }}</span></span>
      <span class="stat-item">{{ horse.surface }} <span class="stars">{{ horse.surface_stars }}</span></span>
      <span class="stat-item">{{ horse.condition }} <span class="stars">{{ horse.condition_stars }}</span></span>
    </p>
    <div class="horse-status {{ horse.status|lower }}">
      {{ horse.status }}
    </div>
    <div class="horse-card-buttons">
        <button class="horse-card-buttons">View Racing Profile</button>
        <button class="horse-card-buttons">View Breeding Profile</button>
    </div>
  </div>
</div>