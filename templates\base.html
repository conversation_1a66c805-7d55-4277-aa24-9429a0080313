<!-- templates/index.html -->
<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.3.1/dist/css/bootstrap.min.css" integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous">
    <title>{% block title %}{% endblock %}</title>
    <style>
        /* CSS Variables for theming */
        :root {
            --bg-color: #ffffff;
            --text-color: #212529;
            --navbar-bg: #fefefe;
            --navbar-text: #212529;
            --navbar-border: #dee2e6;
            --navbar-toggler: #212529;
        }

        /* Dark mode variables */
        [data-theme="dark"] {
            --bg-color: #1a1a1a;
            --text-color: #e9ecef;
            --navbar-bg: #2d2d2d;
            --navbar-text: #e9ecef;
            --navbar-border: #444;
            --navbar-toggler: #e9ecef;
        }

        /* Apply theme variables to body */
        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            padding-top: 70px; /* Adjust this value based on your navbar height */
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        /* Fixed navbar styles with dark mode support */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1030; /* Bootstrap's navbar z-index */
            background-color: var(--navbar-bg) !important;
            border-bottom: 1px solid var(--navbar-border);
            transition: background-color 0.3s ease, border-color 0.3s ease;
        }

        /* Navbar text and brand styling */
        .navbar-brand,
        .navbar-nav .nav-link {
            color: var(--navbar-text) !important;
            transition: color 0.3s ease;
        }

        .navbar-brand:hover,
        .navbar-nav .nav-link:hover {
            color: var(--navbar-text) !important;
            opacity: 0.8;
        }

        /* Navbar toggler for mobile */
        .navbar-toggler {
            border-color: var(--navbar-border);
        }

        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='#{to-rgb(var(--navbar-toggler))}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }

        /* Dark mode navbar toggler icon */
        [data-theme="dark"] .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='%23e9ecef' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }

        /* Make content area take full height and be scrollable */
        .content-wrapper {
            height: calc(100vh - 70px); /* Full viewport height minus navbar height */
            overflow-y: auto; /* Enable vertical scrolling */
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light">
        <a class="navbar-brand" href="#">{% block stable_name %}{% endblock %}</a>
        {% block stable_id %}{% endblock %}

        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarSupportedContent">
            <ul class="navbar-nav mr-auto"></ul>
            {% block update_stable %}
            {% endblock %}   
        </div>
    </nav>

    <!-- Scrollable content wrapper -->
    <div class="content-wrapper">
        {% block content %}
        {% endblock %}
    </div>
    <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.14.7/dist/umd/popper.min.js" integrity="sha384-UO2eT0CpHqdSJQ6hJty5KVphtPhzWj9WO1clHTMGa3JDZwrnQq4sF86dIHNDz0W1" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.3.1/dist/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous"></script>

    <!-- Theme synchronization script -->
    <script>
        // Apply saved theme on page load
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            applyTheme(savedTheme);
        });

        function applyTheme(theme) {
            const body = document.body;
            if (theme === 'dark') {
                body.setAttribute('data-theme', 'dark');
            } else {
                body.removeAttribute('data-theme');
            }
        }

        // Listen for theme changes from other pages
        window.addEventListener('storage', function(e) {
            if (e.key === 'theme') {
                applyTheme(e.newValue);
            }
        });
    </script>
</body>
</html>