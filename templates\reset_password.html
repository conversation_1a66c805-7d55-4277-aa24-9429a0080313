{% extends "base.html" %}
{% block update_stable %}
{% endblock %}
{% block stable_name %}
{% endblock %}
{% block stable_id %}
{% endblock %}
{% block content %}
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Reset Password - Vyperactive Genetics</title>
  <style>
    body {
      margin: 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #ffffff;
    }

    .header {
      padding: 1.5rem 2rem 1rem 2rem;
      border-bottom: 1px solid #ddd;
      font-size: 1.2rem;
    }

    .header b {
      font-weight: 700;
      font-size: 1.4rem;
    }

    .header span {
      font-size: 0.95rem;
      margin-left: 1rem;
      color: #333;
    }

    .container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: calc(100vh - 80px);
    }

    .reset-card {
      width: 320px;
      padding: 2rem;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      border-radius: 10px;
      background-color: white;
      text-align: center;
    }

    .reset-card h2 {
      margin-bottom: 1.5rem;
      font-size: 1.25rem;
      font-weight: bold;
    }

    .reset-card input[type="email"] {
      width: 100%;
      padding: 0.5rem;
      margin-bottom: 0.75rem;
      border: 1px solid #ccc;
      border-radius: 5px;
      font-size: 0.95rem;
    }

    .reset-card button {
      width: 100%;
      padding: 0.6rem;
      background-color: #2d57c8;
      color: white;
      border: none;
      border-radius: 5px;
      font-size: 1rem;
      cursor: pointer;
    }

    .reset-card button:hover {
      background-color: #1f45a4;
    }

    .reset-card .links {
      margin-top: 0.75rem;
      font-size: 0.85rem;
    }

    .reset-card .links a {
      color: #2d57c8;
      text-decoration: none;
    }

    .reset-card .links a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>

  <div class="header">

  </div>

  <div class="container">
    <div class="reset-card">
      <h2>Reset Password</h2>
      <form>
        <input type="email" placeholder="Enter your email" required />
        <button type="submit">Send Reset Link</button>
      </form>
      <div class="links">
        Remembered your password? <a href="/login">Back to Login</a>
      </div>
    </div>
  </div>

</body>
</html>
{% endblock %}  


