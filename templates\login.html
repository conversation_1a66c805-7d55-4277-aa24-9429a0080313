{% extends "base.html" %}
{% block update_stable %}
{% endblock %}
{% block stable_name %}
{% endblock %}
{% block stable_id %}
{% endblock %}
{% block content %}
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Login to Your Stable</title>
  <style>
    body {
      margin: 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #ffffff;
    }

    .header {
      padding: 1.5rem 2rem 1rem 2rem;
      border-bottom: 1px solid #ddd;
      font-size: 1.2rem;
    }

    .header b {
      font-weight: 700;
      font-size: 1.4rem;
    }

    .header span {
      font-size: 0.95rem;
      margin-left: 1rem;
      color: #333;
    }

    .container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: calc(100vh - 80px);
    }

    .login-card {
      width: 320px;
      padding: 2rem;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      border-radius: 10px;
      background-color: white;
      text-align: center;
    }

    .login-card h2 {
      margin-bottom: 1.5rem;
      font-size: 1.25rem;
      font-weight: bold;
    }

    .login-card input[type="email"],
    .login-card input[type="password"] {
      width: 100%;
      padding: 0.5rem;
      margin-bottom: 0.75rem;
      border: 1px solid #ccc;
      border-radius: 5px;
      font-size: 0.95rem;
    }

    .login-card button {
      width: 100%;
      padding: 0.6rem;
      background-color: #2d57c8;
      color: white;
      border: none;
      border-radius: 5px;
      font-size: 1rem;
      cursor: pointer;
    }

    .login-card button:hover {
      background-color: #1f45a4;
    }

    .login-card .links {
      margin-top: 0.75rem;
      font-size: 0.85rem;
    }

    .login-card .links a {
      color: #2d57c8;
      text-decoration: none;
      margin: 0 0.25rem;
    }

    .login-card .links a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>

  <!-- <div class="header">
    <b>Vyperactive Genetics</b>
    <span>Stable Id: 17678f71-353d-4da5-85e7-d1f45b2abef3</span>
  </div> -->

  <div class="container">
    <div class="login-card">
      <h2>Login to Your Stable</h2>
      <form action="#" method="post">
        <input type="email" name="email" placeholder="Email" required />
        <input type="password" name="password" placeholder="Password" required />
        <button type="submit" value="/horse_grid">Login</button>
      </form>
      <div class="links">
        <div><a href="/reset_password">Forgot Password?</a></div>
        <div>Don't have an account? <a href="#">Register</a></div>
      </div>
    </div>
  </div>

</body>
</html>
{% endblock %}

