from request import Request
from Services.save_to_csv import save_to_csv
from flask import Flask, render_template, redirect, url_for, request, jsonify
from Services.load_csv import get_horse_data_from_csv
import os
import ast
import datetime
import json

OUTPUT_CSV_FILE = "updated_horse_data.csv"
TIMESTAMP_FILE = "last_update.json"

def save_update_timestamp():
    """Save the current timestamp when stable was last updated"""
    timestamp_data = {
        "last_updated": datetime.datetime.now().isoformat(),
        "formatted_time": datetime.datetime.now().strftime("%B %d, %Y at %I:%M %p")
    }
    try:
        with open(TIMESTAMP_FILE, 'w') as f:
            json.dump(timestamp_data, f)
    except Exception as e:
        print(f"Error saving timestamp: {e}")

def get_last_update_time():
    """Get the last update timestamp"""
    try:
        if os.path.exists(TIMESTAMP_FILE):
            with open(TIMESTAMP_FILE, 'r') as f:
                data = json.load(f)
                return data.get("formatted_time", "Never updated")
        else:
            return "Never updated"
    except Exception as e:
        print(f"Error reading timestamp: {e}")
        return "Unknown"

# def fetch_api_data(api_url: str, headers: Union[dict, None] = None) -> Union[dict, list, None]:
#     """
#     Fetches data from a given API URL and returns it as a dictionary or list.

#     Args:
#         api_url: The URL of the API endpoint.
#         headers: A dictionary of headers to send with the request.

#     Returns:
#         A dictionary or list containing the data, or None if an error occurs.
#     """
#     try:
#         print(f"Fetching data from {api_url}...")
#         response = requests.get(api_url, headers=headers)
#         # Raise an HTTPError for bad responses (4xx or 5xx)
#         response.raise_for_status()
#         print("Data fetched successfully!")
#         return response.json()
#     except requests.exceptions.RequestException as e:
#         print(f"An error occurred while fetching data: {e}")
#         return None

# IMPORTANT: Replace with your actual API key and the stable ID you want to query.
API_KEY = 'VSy2iJoY5V5et4P23jJLi7ld3JPfuBm73oWAeEYx' # This is a test key from your code
STABLE_ID = "17678ff1-353d-4da5-85e7-01f5a2babef3" # <--- CHANGE THIS

def main():
    '''
    Main function to execute the script.
    '''
    # get_horse_data_from_api()
    # 1. Initialize the request handler with your API key
    request_handler = Request(api_key=API_KEY)

    # 2. Fetch all horses from the specified stable
    all_horses = request_handler.get_horses_by_stable(stable_id=STABLE_ID)

    # 3. If horses were found, print their names and save them to a CSV
    if all_horses:
        print("\n--- Horses Found ---")
        for horse in all_horses:
            print(f"- {horse.name} (ID: {horse.id})")

        # 4. Save the list of horses to a CSV file
        save_to_csv(all_horses, OUTPUT_CSV_FILE)

def main_by_horse_ids():
    '''
    Alternative main function to fetch horses by a list of horse IDs.
    Use this when the stable API is blocked.
    Reads horse IDs from the CSV file and fetches fresh data from API.
    '''
    # Load horse IDs from the CSV file
    csv_file_path = "17678ff1-353d-4da5-85e7-01f5a2babef3-638882372804519157.csv"

    try:
        # Read the CSV file to get horse IDs
        horse_data = get_horse_data_from_csv(csv_file_path)

        if not horse_data:
            print(f"No data found in {csv_file_path}")
            return

        # Extract horse IDs from the 'id' column
        horse_ids = [row.get('id') for row in horse_data if row.get('id')]

        # Filter out any None or empty IDs
        horse_ids = [horse_id for horse_id in horse_ids if horse_id and str(horse_id).strip()]

        print(f"Loaded {len(horse_ids)} horse IDs from {csv_file_path}")

        if not horse_ids:
            print("No valid horse IDs found in the CSV file")
            return

    except Exception as e:
        print(f"Error reading horse IDs from CSV: {e}")
        return

    # 1. Initialize the request handler with your API key
    request_handler = Request(api_key=API_KEY)

    # 2. Fetch horses by their IDs
    horses = request_handler.get_horses_by_ids(horse_ids)

    # 3. If horses were found, print their names and save them to a CSV
    if horses:
        print("\n--- Horses Found by ID ---")
        for horse in horses:
            print(f"- {horse.name} (ID: {horse.id})")

        # 4. Save the list of horses to a CSV file
        save_to_csv(horses, OUTPUT_CSV_FILE)

        # 5. Save the update timestamp
        save_update_timestamp()
        print(f"Stable updated successfully at {get_last_update_time()}")
    else:
        print("No horses were successfully fetched.")

app = Flask(__name__)

@app.route("/")
def home():
    return render_template("index.html")

@app.route("/login", methods=["POST", "GET"])
def login():
    if request.method == "POST":
        user = request.form["email"]
        return redirect(url_for("horse_grid", usr=user))
    else:
        return render_template("login.html")

@app.route("/<usr>")
def user(usr):
    return f"<h1>{usr}</h1>"

@app.route("/reset_password")
def reset_password():
    return render_template("reset_password.html")

@app.route("/register")
def register():
    return render_template("register.html")

def generate_star_display(rating):
    """
    Generate star display based on rating value.
    0 = ☆☆☆ (3 empty stars)
    1.5 = ⭐★☆ (1 full, 1 half, 1 empty)
    3.0 = ⭐⭐⭐ (3 full stars)
    """
    try:
        rating = float(rating)
        full_stars = int(rating)
        half_star = (rating - full_stars) >= 0.5

        # Build star string
        stars = ""
        for i in range(3):
            if i < full_stars:
                stars += "⭐"   # Full star
            elif i == full_stars and half_star:
                stars += "★"  # Half star (different character)
            else:
                stars += "☆"  # Empty star

        return stars
    except (ValueError, TypeError):
        return "☆☆☆"  # Default to empty stars if invalid rating

def extract_direction_value(direction_str):
    """Extract direction value - now it's a simple string like 'RightTurning' or 'LeftTurning'"""
    if isinstance(direction_str, str) and direction_str.strip():
        # Remove 'Turning' suffix to get 'Right' or 'Left'
        return direction_str.replace('Turning', '').strip()
    return 'N/A'

def grade_to_stars(grade_str):
    """Convert racing grade to star rating (0-3 stars)"""
    if not grade_str or grade_str == 'N/A':
        return 0

    grade_map = {
        'S+': 3.0, 'S': 2.5, 'S-': 2.0,
        'A+': 2.0, 'A': 1.5, 'A-': 1.0,
        'B+': 1.0, 'B': 0.5, 'B-': 0.5,
        'C+': 0.5, 'C': 0.0, 'C-': 0.0,
        'D+': 0.0, 'D': 0.0, 'D-': 0.0,
        'F': 0.0
    }
    return grade_map.get(str(grade_str).strip(), 0)

def extract_direction_weight(direction_str):
    """Extract direction preference weight from racing stats"""
    # For new CSV format, we'll use the racing_start grade as direction preference
    return 0  # Will be calculated from racing stats in map_csv_row_to_horse_card

def extract_surface_value(surface_str):
    """Extract surface value - now it's a simple string"""
    if isinstance(surface_str, str) and surface_str.strip():
        return surface_str.strip()
    return 'N/A'

def extract_surface_weight(surface_str):
    """Extract surface preference weight from racing stats"""
    # For new CSV format, we'll use racing grades
    return 0  # Will be calculated from racing stats in map_csv_row_to_horse_card

def extract_condition_value(condition_str):
    """Extract condition value - now it's a simple string"""
    if isinstance(condition_str, str) and condition_str.strip():
        return condition_str.strip()
    return 'N/A'

def extract_condition_weight(condition_str):
    """Extract condition preference weight from racing stats"""
    # For new CSV format, we'll use racing grades
    return 0  # Will be calculated from racing stats in map_csv_row_to_horse_card

def map_csv_row_to_horse_card(row):
    # Map CSV row to the fields expected by horse_card.html
    # Provide defaults if fields are missing
    life_status_map = {
        1: 'Foal',
        2: 'Racing',
        3: 'Retired',
        4: 'Deceased'
    }
    # Get the integer value, default to 1 (Foal) if missing or invalid
    try:
        life_status_int = int(row.get('lifeStatus', 1))
    except ValueError:
        life_status_int = 1
    status = life_status_map.get(life_status_int, 'Unknown')

    # Handle both boolean and string values for canEnterNewRace
    can_enter_race = row.get('canEnterNewRace', False)
    if isinstance(can_enter_race, str):
        can_enter_race = can_enter_race.lower() == 'true'
    elif can_enter_race is None:
        can_enter_race = False

    if status == 'Racing' and can_enter_race:
        status = 'Ready To Race'
    elif status == 'Racing' and not can_enter_race:
        status = 'Exhausted'

    is_pregnant = row.get('isPregnant', False)
    if isinstance(is_pregnant, str):
        is_pregnant = is_pregnant.lower() == 'true'
    elif is_pregnant is None or is_pregnant == '':
        is_pregnant = False

    # Determine gender first
    gender = row.get('gender', 'Male')
    if gender == 0:
        gender = 'Male'
    else:
        gender = 'Female'

    # Only female horses can be pregnant
    if status == 'Retired' and is_pregnant and gender == 'Female':
        status = 'Pregnant'


    # Extract racing data (new CSV format)
    direction_str = row.get('racing_direction', '')
    direction = extract_direction_value(direction_str)

    surface_str = row.get('racing_surface', '')
    surface = extract_surface_value(surface_str)

    condition_str = row.get('racing_condition', '')
    condition = extract_condition_value(condition_str)

    # Calculate star ratings from racing grades
    direction_weight = grade_to_stars(row.get('racing_start', ''))  # Use start for direction preference
    surface_weight = grade_to_stars(row.get('racing_speed', ''))    # Use speed for surface preference
    condition_weight = grade_to_stars(row.get('racing_stamina', '')) # Use stamina for condition preference

    # Debug logging for El Domador
    if row.get('name', '').lower() == 'el domador':
        print(f"El Domador Debug:")
        print(f"  racing_start: '{row.get('racing_start', '')}' -> {direction_weight} stars")
        print(f"  racing_speed: '{row.get('racing_speed', '')}' -> {surface_weight} stars")
        print(f"  racing_stamina: '{row.get('racing_stamina', '')}' -> {condition_weight} stars")
        print(f"  Total stars: {direction_weight + surface_weight + condition_weight}")

    image_url = f"/static/images/{row.get('name', '').lower().replace(' ', '_')}.png"
    if not os.path.exists(image_url):
        image_url = "/static/images/missing_horse.png"

    # Calculate total stars for preference filtering and gold border
    total_stars = direction_weight + surface_weight + condition_weight
    has_gold_border = total_stars == 9.0

    return {
        'name': row.get('name', 'Unknown'),
        'image_url': image_url,
        'grade': row.get('racing_grade', 'N/A'),
        'type': row.get('gender', 'N/A'),
        'age': row.get('age', 'N/A'),
        'gender': gender,
        'direction': direction,
        'surface': surface,
        'condition':condition,
        'direction_stars': generate_star_display(direction_weight),
        'surface_stars': generate_star_display(surface_weight),
        'condition_stars': generate_star_display(condition_weight),
        'total_stars': total_stars,
        'has_gold_border': has_gold_border,
        'status': status
    }

@app.route("/horse_grid")
def horse_grid():
    csv_path = os.path.join(os.path.dirname(__file__), 'updated_horse_data.csv')
    horse_rows = get_horse_data_from_csv(csv_path)
    horses = [map_csv_row_to_horse_card(row) for row in horse_rows]
    # Sort horses by name alphabetically
    horses.sort(key=lambda horse: horse['name'].lower())

    # Get last update time for display
    last_update = get_last_update_time()

    return render_template('horse_grid.html', horses=horses, last_update=last_update)

@app.route("/update_stable", methods=['POST'])
def update_stable():
    """Handle Update Stable button click"""
    try:
        # Call main_by_horse_ids to fetch fresh data
        main_by_horse_ids()

        # Return success response with new timestamp
        return jsonify({
            'success': True,
            'message': 'Stable updated successfully!',
            'last_update': get_last_update_time()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error updating stable: {str(e)}'
        }), 500

if __name__ == "__main__":
    # Choose which method to use:
    # main()  # Use this for fetching by stable ID (when API allows it)
    #main_by_horse_ids()  # Use this for fetching by horse IDs (when stable API is blocked)
    app.run(debug=True)