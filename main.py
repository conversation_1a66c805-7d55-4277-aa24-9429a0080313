from request import Request
from Services.save_to_csv import save_to_csv
from flask import Flask, render_template, redirect, url_for, request
from Services.load_csv import get_horse_data_from_csv
import os
import ast

OUTPUT_CSV_FILE = "horse_data.csv"

# def fetch_api_data(api_url: str, headers: Union[dict, None] = None) -> Union[dict, list, None]:
#     """
#     Fetches data from a given API URL and returns it as a dictionary or list.

#     Args:
#         api_url: The URL of the API endpoint.
#         headers: A dictionary of headers to send with the request.

#     Returns:
#         A dictionary or list containing the data, or None if an error occurs.
#     """
#     try:
#         print(f"Fetching data from {api_url}...")
#         response = requests.get(api_url, headers=headers)
#         # Raise an HTTPError for bad responses (4xx or 5xx)
#         response.raise_for_status()
#         print("Data fetched successfully!")
#         return response.json()
#     except requests.exceptions.RequestException as e:
#         print(f"An error occurred while fetching data: {e}")
#         return None

# IMPORTANT: Replace with your actual API key and the stable ID you want to query.
API_KEY = 'VSy2iJoY5V5et4P23jJLi7ld3JPfuBm73oWAeEYx' # This is a test key from your code
STABLE_ID = "17678ff1-353d-4da5-85e7-01f5a2babef3" # <--- CHANGE THIS

def main():
    '''
    Main function to execute the script.
    '''
    # get_horse_data_from_api()
    # 1. Initialize the request handler with your API key
    request_handler = Request(api_key=API_KEY)

    # 2. Fetch all horses from the specified stable
    all_horses = request_handler.get_horses_by_stable(stable_id=STABLE_ID)

    # 3. If horses were found, print their names and save them to a CSV
    if all_horses:
        print("\n--- Horses Found ---")
        for horse in all_horses:
            print(f"- {horse.name} (ID: {horse.id})")

        # 4. Save the list of horses to a CSV file
        save_to_csv(all_horses, OUTPUT_CSV_FILE)

def main_by_horse_ids():
    '''
    Alternative main function to fetch horses by a list of horse IDs.
    Use this when the stable API is blocked.
    '''
    # Example horse IDs - replace with your actual horse IDs
    # You can get these IDs from:
    # 1. Previous CSV exports when the stable API was working
    # 2. The Photo Finish Live website
    # 3. Other API endpoints that might still be accessible
    horse_ids = [
        "3fc2e4f6-e738-4e14-8ac8-02d1b7704088",
        "3a95b614-5d46-4f7a-b7f0-4e4bbf2a1ed3",
        "ca9ddf45-27f0-4ee8-aee4-8c1d9f12cf5f",
    ]

    # 1. Initialize the request handler with your API key
    request_handler = Request(api_key=API_KEY)

    # 2. Fetch horses by their IDs
    horses = request_handler.get_horses_by_ids(horse_ids)

    # 3. If horses were found, print their names and save them to a CSV
    if horses:
        print("\n--- Horses Found by ID ---")
        for horse in horses:
            print(f"- {horse.name} (ID: {horse.id})")

        # 4. Save the list of horses to a CSV file
        save_to_csv(horses, OUTPUT_CSV_FILE)
    else:
        print("No horses were successfully fetched.")

app = Flask(__name__)

@app.route("/")
def home():
    return render_template("index.html")

@app.route("/login", methods=["POST", "GET"])
def login():
    if request.method == "POST":
        user = request.form["email"]
        return redirect(url_for("horse_grid", usr=user))
    else:
        return render_template("login.html")

@app.route("/<usr>")
def user(usr):
    return f"<h1>{usr}</h1>"

@app.route("/reset_password")
def reset_password():
    return render_template("reset_password.html")

@app.route("/register")
def register():
    return render_template("register.html")

def extract_direction_value(direction_str):
    try:
        # Convert string to dictionary
        direction_dict = ast.literal_eval(direction_str)
        value = direction_dict.get('value', 'N/A')

        return value.replace('Turning', '') if isinstance(value, str) else value
    except Exception:
        return 'N/A'
    
def extract_surface_value(surface_str):
    try:
        surface_dict = ast.literal_eval(surface_str)
        return surface_dict.get('value', 'N/A')
    except Exception:
        return 'N/A'
    
def extract_condition_value(condition_str):
    try:
        condition_dict = ast.literal_eval(condition_str)
        return condition_dict.get('value', 'N/A')
    except Exception:
        return 'N/A'

def map_csv_row_to_horse_card(row):
    # Map CSV row to the fields expected by horse_card.html
    # Provide defaults if fields are missing
    life_status_map = {
        1: 'Foal',
        2: 'Racing',
        3: 'Retired',
        4: 'Deceased'
    }
    # Get the integer value, default to 1 (Foal) if missing or invalid
    try:
        life_status_int = int(row.get('lifeStatus', 1))
    except ValueError:
        life_status_int = 1
    status = life_status_map.get(life_status_int, 'Unknown')

    # Handle both boolean and string values for canEnterNewRace
    can_enter_race = row.get('canEnterNewRace', False)
    if isinstance(can_enter_race, str):
        can_enter_race = can_enter_race.lower() == 'true'
    elif can_enter_race is None:
        can_enter_race = False

    if status == 'Racing' and can_enter_race:
        status = 'Ready To Race'
    elif status == 'Racing' and not can_enter_race:
        status = 'Exhausted'

    is_pregnant = row.get('isPregnant', False)
    print(f"is_pregnant: {is_pregnant}")
    if isinstance(is_pregnant, str):
        is_pregnant = is_pregnant.lower() == 'true'
    elif is_pregnant is None or is_pregnant == '':
        is_pregnant = False

    # Determine gender first
    gender = row.get('gender', 'Male')
    if gender == 0:
        gender = 'Male'
    else:
        gender = 'Female'

    # Only female horses can be pregnant
    if status == 'Retired' and is_pregnant and gender == 'Female':
        status = 'Pregnant'


    direction_str = row.get('racing_direction', '{}')
    direction = extract_direction_value(direction_str)

    surface_str = row.get('racing_surface', '{}')
    surface = extract_surface_value(surface_str)

    condition_str = row.get('racing_condition', '{}')
    condition = extract_condition_value(condition_str)

    image_url = f"/static/images/{row.get('name', '').lower().replace(' ', '_')}.png"
    if not os.path.exists(image_url):
        image_url = "/static/images/missing_horse.png"

    return {
        'name': row.get('name', 'Unknown'),
        'image_url': image_url,
        'grade': row.get('racing_grade', 'N/A'),
        'type': row.get('gender', 'N/A'),
        'age': row.get('age', 'N/A'),
        'gender': gender,
        'direction': direction,
        'surface': surface,
        'condition':condition,
        'direction_stars': '★☆☆',  # Placeholder, can be calculated
        'surface_stars': '★★☆',    # Placeholder, can be calculated
        'condition_stars': '★★☆',  # Placeholder, can be calculated
        'status': status
    }

@app.route("/horse_grid")
def horse_grid():
    csv_path = os.path.join(os.path.dirname(__file__), 'updated_horse_data.csv')
    horse_rows = get_horse_data_from_csv(csv_path)
    horses = [map_csv_row_to_horse_card(row) for row in horse_rows]
    # Sort horses by name alphabetically
    horses.sort(key=lambda horse: horse['name'].lower())
    return render_template('horse_grid.html', horses=horses)

if __name__ == "__main__":
    # Choose which method to use:
    # main()  # Use this for fetching by stable ID (when API allows it)
    #main_by_horse_ids()  # Use this for fetching by horse IDs (when stable API is blocked)
    app.run(debug=True)