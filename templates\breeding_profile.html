{% extends "base.html" %}

{% block title %}{{ horse.name }} - Breeding Profile{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Back to Stable Button -->
    <div class="row mb-3">
        <div class="col-12">
            <a href="{{ url_for('horse_grid') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Stable
            </a>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-4">{{ horse.name }}</h1>
            <p class="lead text-muted">Breeding Profile</p>
        </div>
    </div>

    <!-- Breeding Card Section -->
    <div class="row justify-content-center">
        <div class="col-auto">
            {% include 'breeding_card.html' %}
        </div>
    </div>

    <!-- Additional Breeding Information -->
    <div class="row mt-5">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Breeding Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Gender:</strong>
                        </div>
                        <div class="col-sm-6">
                            {{ horse.gender }}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-sm-6">
                            <strong>Age:</strong>
                        </div>
                        <div class="col-sm-6">
                            {{ horse.age }} years old
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-sm-6">
                            <strong>Status:</strong>
                        </div>
                        <div class="col-sm-6">
                            <span class="badge badge-{{ 'success' if horse.status == 'Racing' else 'secondary' }}">
                                {{ horse.status }}
                            </span>
                        </div>
                    </div>
                    {% if horse.gender == 'Mare' %}
                    <div class="row mt-2">
                        <div class="col-sm-6">
                            <strong>Pregnant:</strong>
                        </div>
                        <div class="col-sm-6">
                            {{ 'Yes' if horse.pregnant else 'No' }}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Racing Preferences</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Direction:</strong>
                        </div>
                        <div class="col-sm-6">
                            {{ horse.direction }} {{ horse.direction_stars }}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-sm-6">
                            <strong>Surface:</strong>
                        </div>
                        <div class="col-sm-6">
                            {{ horse.surface }} {{ horse.surface_stars }}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-sm-6">
                            <strong>Condition:</strong>
                        </div>
                        <div class="col-sm-6">
                            {{ horse.condition }} {{ horse.condition_stars }}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-sm-6">
                            <strong>Total Stars:</strong>
                        </div>
                        <div class="col-sm-6">
                            <strong>{{ horse.total_stars }}/9</strong>
                            {% if horse.has_gold_border %}
                            <span class="text-warning">⭐ Perfect Rating!</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Racing Stats Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Racing Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary">{{ horse.racing_start|default('--') }}</h4>
                                <small class="text-muted">Start</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary">{{ horse.racing_speed|default('--') }}</h4>
                                <small class="text-muted">Speed</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success">{{ horse.racing_stamina|default('--') }}</h4>
                                <small class="text-muted">Stamina</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary">{{ horse.racing_finish|default('--') }}</h4>
                                <small class="text-muted">Finish</small>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h4 class="text-primary">{{ horse.racing_heart|default('--') }}</h4>
                                <small class="text-muted">Heart</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h4 class="text-success">{{ horse.racing_temper|default('--') }}</h4>
                                <small class="text-muted">Temper</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h4 class="text-secondary">{{ horse.grade|default('--') }}</h4>
                                <small class="text-muted">Overall Grade</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4 mb-5">
        <div class="col-12 text-center">
            <button class="btn btn-primary btn-lg mr-3" onclick="viewRacingProfile('{{ horse.name }}')">
                <i class="fas fa-horse"></i> View Racing Profile
            </button>
            <button class="btn btn-success btn-lg" onclick="startBreeding('{{ horse.name }}')">
                <i class="fas fa-heart"></i> Start Breeding
            </button>
        </div>
    </div>
</div>

<script>
function viewRacingProfile(horseName) {
    // TODO: Implement racing profile view
    alert('Racing profile for ' + horseName + ' - Coming soon!');
}

function startBreeding(horseName) {
    // TODO: Implement breeding functionality
    alert('Breeding for ' + horseName + ' - Coming soon!');
}
</script>
{% endblock %}
