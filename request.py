import requests
import json # To pretty-print the JSON response
import time  # For adding delays between requests
from horse import HorseData
from racing import RacingData
from Services.save_to_csv import save_to_csv
from Services.load_csv import get_horse_data_from_csv
from typing import List, Dict, Any, Optional



class Request:
    # Set the required headers for authentication
    BASE_API_URL = "https://api.photofinish.live/pfl-pro/stable/"
    HORSE_API_URL = "https://api.photofinish.live/pfl-pro/horse-api/"

    def __init__(self, api_key: str):
        self.payload={}
        self.headers = {
            "Content-Type": "application/json",
            "x-api-key": 'VSy2iJoY5V5et4P23jJLi7ld3JPfuBm73oWAeEYx'
        }

        self.horses: List[HorseData] = []

        #self.api_url= "https://api.photofinish.live/pfl-pro/horse-api/3fc2e4f6-e738-4e14-8ac8-02d1b7704088" 
        # """Class to fetch a horse object from the Photo Finish API."""   
        # self.horse_dict = self.load_from_csv()
        # if not self.horse_dict:
        #     self.horse_dict = self.load_from_api()

    
    def get_horses_by_stable(self, stable_id: str) -> List[HorseData]:
        """
        Fetches all horses for a given stable ID from the API.
        Args:
            stable_id: The ID of the stable to fetch horses from.
       Returns:
            A list of HorseData objects, or an empty list if an error occurs.
        """
        api_url = f"{self.BASE_API_URL}{stable_id}"
        print(f"Fetching horses from stable: {stable_id}...")

        try:
            response = requests.get(api_url, headers=self.headers)
            # Raise an exception for bad status codes (4xx or 5xx)
            response.raise_for_status()
            
            # The API returns a dictionary with a 'horses' key containing the list
            horse_list_json = response.json().get("horses", [])
            
            # Convert each horse dictionary into a HorseData object
            self.horses = [HorseData(horse_data) for horse_data in horse_list_json]
            
            print(f"Successfully fetched {len(self.horses)} horses.")
            return self.horses
        except requests.exceptions.RequestException as e:
            print(f"An error occurred while fetching data: {e}")
            return []

    def get_horses_by_ids(self, horse_ids: List[str]) -> List[HorseData]:
        """
        Fetches horses by a list of horse IDs from the API.
        Args:
            horse_ids: A list of horse IDs to fetch.
        Returns:
            A list of HorseData objects, or an empty list if an error occurs.
        """
        if not horse_ids:
            print("No horse IDs provided.")
            return []

        print(f"Fetching {len(horse_ids)} horses by ID...")
        horses = []

        for i, horse_id in enumerate(horse_ids):
            try:
                # Add delay between requests to avoid rate limiting (except for first request)
                if i > 0:
                    print(f"Waiting 0.25 seconds before next request...")
                    time.sleep(0.25)

                api_url = f"{self.HORSE_API_URL}{horse_id}"
                response = requests.get(api_url, headers=self.headers)
                response.raise_for_status()

                # The horse API returns the horse data directly
                horse_data = response.json()
                horse = HorseData(horse_data)
                horses.append(horse)
                print(f"✓ Fetched horse: {horse.name} (ID: {horse.id})")

            except requests.exceptions.RequestException as e:
                print(f"✗ Failed to fetch horse ID {horse_id}: {e}")
                continue

        self.horses = horses
        print(f"Successfully fetched {len(horses)} out of {len(horse_ids)} horses.")
        return horses
    
    def save(self):
        horse_obj, racing_obj = self.get_horse_data()
        save_to_csv(horse_obj, "horse_data.csv")

    def load_from_csv(self):
        return get_horse_data_from_csv("C:/Users/<USER>/Desktop/Stable Manager/horse_data.csv")

    def load_from_api(self):
        
        # Fetch the data
        self.response = requests.request("GET", self.api_url, headers=self.headers, data=self.payload)         

        return self.response.json()


