{% extends "base.html" %}
{% block content %}

<link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
<style>
.grid-container {
    display: flex;
    flex-wrap: wrap;
    overflow-y: auto;
    max-height: fit-content;
    max-width: fit-content;
    padding: 15px;
}

/* Filter button styles */
.filter-container {
    padding: 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 100;
}

.filter-btn {
    margin: 2px;
    border-radius: 20px;
    font-size: 14px;
    padding: 8px 16px;
    transition: all 0.2s ease;
}

.filter-btn.active {
    background-color: #007bff !important;
    border-color: #007bff !important;
    color: white !important;
}

.filter-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,5);
}

/* Hide filtered horses */
.horse-card.hidden {
    display: none !important;
}
</style>

<!-- Filter Buttons -->
<div class="filter-container">
    <div class="d-flex flex-wrap align-items-center">
        <span class="mr-3 font-weight-bold">Filters:</span>
        <button class="btn btn-outline-secondary filter-btn" data-filter="all">All</button>
        <button class="btn btn-outline-primary filter-btn" data-filter="foal">Foal</button>
        <button class="btn btn-outline-success filter-btn" data-filter="racing">Racing</button>
        <button class="btn btn-outline-warning filter-btn" data-filter="retired">Retired</button>
        <button class="btn btn-outline-dark filter-btn" data-filter="deceased">Deceased</button>
        <button class="btn btn-outline-info filter-btn" data-filter="pregnant">Pregnant</button>
        <button class="btn btn-outline-danger filter-btn" data-filter="mares">Mares</button>
        <button class="btn btn-outline-secondary filter-btn" data-filter="stallions">Stallions</button>
    </div>
</div>

<div class="grid-container">
{% for horse in horses %}
    {% include 'horse_card.html' %}
{% endfor %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const horseCards = document.querySelectorAll('.horse-card');

    // Set "All" as active by default
    document.querySelector('[data-filter="all"]').classList.add('active');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');

            // Remove active class from all buttons
            filterButtons.forEach(btn => btn.classList.remove('active'));

            // Add active class to clicked button
            this.classList.add('active');

            // Filter horse cards
            horseCards.forEach(card => {
                let shouldShow = false;

                if (filter === 'all') {
                    shouldShow = true;
                } else if (filter === 'foal') {
                    shouldShow = card.getAttribute('data-status') === 'foal';
                } else if (filter === 'racing') {
                    const status = card.getAttribute('data-status');
                    shouldShow = status === 'racing' || status === 'ready to race' || status === 'exhausted';
                } else if (filter === 'retired') {
                    shouldShow = card.getAttribute('data-status') === 'retired';
                } else if (filter === 'deceased') {
                    shouldShow = card.getAttribute('data-status') === 'deceased';
                } else if (filter === 'pregnant') {
                    shouldShow = card.getAttribute('data-status') === 'pregnant';
                } else if (filter === 'mares') {
                    const gender = card.getAttribute('data-gender');
                    shouldShow = gender === 'filly' || gender === 'mare' || gender == 'female';
                } else if (filter === 'stallions') {
                    const gender = card.getAttribute('data-gender');
                    shouldShow = gender === 'colt' || gender === 'stallion' || gender == 'male';
                }

                if (shouldShow) {
                    card.classList.remove('hidden');
                } else {
                    card.classList.add('hidden');
                }
            });

            // Update button text with count
            updateFilterCounts();
        });
    });

    function updateFilterCounts() {
        filterButtons.forEach(button => {
            const filter = button.getAttribute('data-filter');
            let count = 0;

            if (filter === 'all') {
                count = horseCards.length;
            } else {
                horseCards.forEach(card => {
                    let matches = false;

                    if (filter === 'foal') {
                        matches = card.getAttribute('data-status') === 'foal';
                    } else if (filter === 'racing') {
                        const status = card.getAttribute('data-status');
                        matches = status === 'racing' || status === 'ready to race' || status === 'exhausted';
                    } else if (filter === 'retired') {
                        matches = card.getAttribute('data-status') === 'retired';
                    } else if (filter === 'deceased') {
                        matches = card.getAttribute('data-status') === 'deceased';
                    } else if (filter === 'pregnant') {
                        matches = card.getAttribute('data-pregnant') === 'true';
                    } else if (filter === 'mares') {
                        const gender = card.getAttribute('data-gender');
                        matches = gender === 'filly' || gender === 'mare';
                    } else if (filter === 'stallions') {
                        const gender = card.getAttribute('data-gender');
                        matches = gender === 'colt' || gender === 'stallion';
                    }

                    if (matches) count++;
                });
            }

            // Update button text with count
            const originalText = button.textContent.split(' (')[0];
            button.textContent = `${originalText} (${count})`;
        });
    }

    // Initialize counts
    updateFilterCounts();
});
</script>

{% endblock %}
{% block update_stable %}
    <button type="button" class="btn btn-outline-primary">Update Stable</button>
{% endblock %}
{% block stable_name %}
<h3>Vyperactive Genetics</h3>
{% endblock %}
{% block stable_id %}
<h8>Stable ID: 17678ff1-353d-4da5-85e7-01f5a2babef3</h8>
{% endblock %}