{% extends "base.html" %}
{% block content %}

<link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
<style>
/* CSS Variables for theming */
:root {
    --bg-color: #ffffff;
    --text-color: #212529;
    --card-bg: #ffffff;
    --card-border: #ccc;
    --filter-bg: #f8f9fa;
    --filter-border: #dee2e6;
    --filter-text: #495057;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --gold-shadow: rgba(255, 215, 0, 0.3);
}

/* Dark mode variables */
[data-theme="dark"] {
    --bg-color: #1a1a1a;
    --text-color: #e9ecef;
    --card-bg: #2d2d2d;
    --card-border: #444;
    --filter-bg: #2d2d2d;
    --filter-border: #444;
    --filter-text: #e9ecef;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --gold-shadow: rgba(255, 215, 0, 0.5);
}

/* Apply theme variables */
body {
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.grid-container {
    display: flex;
    flex-wrap: wrap;
    overflow-y: auto;
    max-height: fit-content;
    max-width: fit-content;
    padding: 15px;
}

/* Filter button styles */
.filter-container {
    padding: 15px;
    background-color: var(--filter-bg);
    border-bottom: 1px solid var(--filter-border);
    position: sticky;
    top: 0;
    z-index: 100;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.filter-btn {
    margin: 2px;
    border-radius: 20px;
    font-size: 14px;
    padding: 8px 16px;
    transition: all 0.2s ease;
}

.filter-btn.active {
    background-color: #007bff !important;
    border-color: #007bff !important;
    color: white !important;
}

.filter-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,5);
}

/* Hide filtered horses */
.horse-card.hidden {
    display: none !important;
}

/* Filter summary styles */
.filter-summary {
    background-color: var(--filter-bg);
    border-bottom: 1px solid var(--filter-border);
    padding: 10px 15px;
    text-align: center;
    position: sticky;
    top: 70px; /* Position below the filter container */
    z-index: 99;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.filter-count-text {
    font-weight: 600;
    color: var(--filter-text);
    font-size: 14px;
    transition: color 0.3s ease;
}

/* Reset filters button */
.reset-filters-btn {
    font-weight: bold;
    border-width: 2px;
}

.reset-filters-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* Dark mode toggle styles */
.theme-toggle-container {
    display: flex;
    align-items: center;
    margin-right: 10px;
}

.theme-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    margin-right: 8px;
}

.theme-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.3s;
    border-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 2px;
}

.slider-icon {
    font-size: 14px;
    transition: 0.3s;
    margin-left: 2px;
}

.theme-toggle input:checked + .slider {
    background-color: #2196F3;
    justify-content: flex-end;
}

.theme-toggle input:checked + .slider .slider-icon {
    margin-left: 0;
    margin-right: 2px;
}

.theme-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--filter-text);
    transition: color 0.3s ease;
}

/* Search bar styles */
.search-container {
    position: relative;
    display: flex;
    align-items: center;
    margin-right: 10px;
}

.search-input {
    width: 250px;
    height: 32px;
    font-size: 14px;
    border: 1px solid var(--filter-border);
    background-color: var(--filter-bg);
    color: var(--filter-text);
    border-radius: 20px;
    padding: 6px 35px 6px 15px;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    background-color: var(--card-bg);
}

.search-input::placeholder {
    color: var(--filter-text);
    opacity: 0.7;
}

.clear-search-btn {
    position: absolute;
    right: 5px;
    width: 22px;
    height: 22px;
    border-radius: 50%;
    border: none;
    background-color: transparent;
    color: var(--filter-text);
    font-size: 16px;
    line-height: 1;
    padding: 0;
    display: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.clear-search-btn:hover {
    background-color: var(--filter-border);
    color: var(--text-color);
}

.clear-search-btn.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Update stable button and timestamp styles */
.last-update-text {
    font-size: 13px;
    white-space: nowrap;
}

#update-stable-btn {
    position: relative;
    min-width: 120px;
}

#update-stable-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

#update-stable-btn .spinner-border-sm {
    width: 1rem;
    height: 1rem;
}
</style>

<!-- Filter Buttons -->
<div class="filter-container">
    <div class="d-flex flex-wrap align-items-center">
        <!-- Dark Mode Toggle -->
        <div class="theme-toggle-container">
            <label class="theme-toggle">
                <input type="checkbox" id="theme-toggle" />
                <span class="slider">
                    <span class="slider-icon">🌙</span>
                </span>
            </label>
            <span class="theme-label">Dark Mode</span>
        </div>
        <span class="mx-3 text-muted">|</span>
        <!-- Search Bar -->
        <div class="search-container">
            <input type="text" id="horse-search" class="form-control search-input" placeholder="🔍 Search horses by name..." />
            <button type="button" id="clear-search" class="btn btn-sm btn-outline-secondary clear-search-btn" title="Clear search">×</button>
        </div>
        <span class="mx-3 text-muted">|</span>
        <button class="btn btn-outline-danger filter-btn reset-filters-btn" id="reset-filters">Reset All Filters</button>
        <span class="mx-3 text-muted">|</span>
        <span class="mr-3 font-weight-bold">Status:</span>
        <button class="btn btn-outline-secondary filter-btn status-filter" data-filter="all">All</button>
        <button class="btn btn-outline-primary filter-btn status-filter" data-filter="foal">Foal</button>
        <button class="btn btn-outline-success filter-btn status-filter" data-filter="racing">Racing</button>
        <button class="btn btn-outline-warning filter-btn status-filter" data-filter="retired">Retired</button>
        <button class="btn btn-outline-secondary filter-btn status-filter" data-filter="deceased">Deceased</button>
        <button class="btn btn-outline-info filter-btn status-filter" data-filter="pregnant">Pregnant</button>
        <span class="mx-3 text-muted">|</span>
        <span class="mr-2 font-weight-bold">Gender:</span>
        <button class="btn btn-outline-secondary filter-btn gender-filter" data-filter="all-gender">All</button>
        <button class="btn btn-outline-danger filter-btn gender-filter" data-filter="mares">Mares</button>
        <button class="btn btn-outline-primary filter-btn gender-filter" data-filter="stallions">Stallions</button>
        <span class="mx-3 text-muted">|</span>
        <span class="mr-2 font-weight-bold">Age:</span>
        <button class="btn btn-outline-secondary filter-btn age-filter" data-filter="all-age">All</button>
        <button class="btn btn-outline-info filter-btn age-filter" data-filter="newborns">Newborns</button>
        <button class="btn btn-outline-success filter-btn age-filter" data-filter="yearlings">Yearlings</button>
        <button class="btn btn-outline-warning filter-btn age-filter" data-filter="juveniles">Juveniles</button>
        <button class="btn btn-outline-primary filter-btn age-filter" data-filter="mature3">Mature 3</button>
        <button class="btn btn-outline-secondary filter-btn age-filter" data-filter="mature4-5">Mature 4-5</button>
        <button class="btn btn-outline-danger filter-btn age-filter" data-filter="mature6-8">Mature 6-8</button>
        <span class="mx-3 text-muted">|</span>
        <span class="mr-2 font-weight-bold">Preference:</span>
        <button class="btn btn-outline-secondary filter-btn preference-filter" data-filter="all-preference">All</button>
        <button class="btn btn-outline-warning filter-btn preference-filter" data-filter="under5">&lt;5 Stars</button>
        <button class="btn btn-outline-info filter-btn preference-filter" data-filter="mid-range">5.5-7.5 Stars</button>
        <button class="btn btn-outline-success filter-btn preference-filter" data-filter="high-range">8-8.5 Stars</button>
        <button class="btn btn-outline-warning filter-btn preference-filter" data-filter="perfect">9 Stars</button>
        <span class="mx-3 text-muted">|</span>
        <span class="mr-2 font-weight-bold">Direction:</span>
        <button class="btn btn-outline-secondary filter-btn direction-filter" data-filter="all-direction">All</button>
        <button class="btn btn-outline-primary filter-btn direction-filter" data-filter="left">Left</button>
        <button class="btn btn-outline-success filter-btn direction-filter" data-filter="right">Right</button>
        <span class="mx-3 text-muted">|</span>
        <span class="mr-2 font-weight-bold">Surface:</span>
        <button class="btn btn-outline-secondary filter-btn surface-filter" data-filter="all-surface">All</button>
        <button class="btn btn-outline-warning filter-btn surface-filter" data-filter="dirt">Dirt</button>
        <button class="btn btn-outline-success filter-btn surface-filter" data-filter="turf">Turf</button>
        <span class="mx-3 text-muted">|</span>
        <span class="mr-2 font-weight-bold">Condition:</span>
        <button class="btn btn-outline-secondary filter-btn condition-filter" data-filter="all-condition">All</button>
        <button class="btn btn-outline-info filter-btn condition-filter" data-filter="firm">Firm</button>
        <button class="btn btn-outline-primary filter-btn condition-filter" data-filter="soft">Soft</button>
    </div>
</div>

<!-- Filter Results Summary -->
<div class="filter-summary">
    <span id="filter-count" class="filter-count-text">Showing all horses</span>
</div>

<div class="grid-container">
{% for horse in horses %}
    {% include 'horse_card.html' %}
{% endfor %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const statusFilterButtons = document.querySelectorAll('.status-filter');
    const genderFilterButtons = document.querySelectorAll('.gender-filter');
    const ageFilterButtons = document.querySelectorAll('.age-filter');
    const preferenceFilterButtons = document.querySelectorAll('.preference-filter');
    const directionFilterButtons = document.querySelectorAll('.direction-filter');
    const surfaceFilterButtons = document.querySelectorAll('.surface-filter');
    const conditionFilterButtons = document.querySelectorAll('.condition-filter');
    const horseCards = document.querySelectorAll('.horse-card');

    // Set defaults as active
    document.querySelector('[data-filter="all"]').classList.add('active');
    document.querySelector('[data-filter="all-gender"]').classList.add('active');
    document.querySelector('[data-filter="all-age"]').classList.add('active');
    document.querySelector('[data-filter="all-preference"]').classList.add('active');
    document.querySelector('[data-filter="all-direction"]').classList.add('active');
    document.querySelector('[data-filter="all-surface"]').classList.add('active');
    document.querySelector('[data-filter="all-condition"]').classList.add('active');

    // Handle status filter clicks
    statusFilterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all status buttons
            statusFilterButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            applyFilters();
        });
    });

    // Handle gender filter clicks
    genderFilterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all gender buttons
            genderFilterButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            applyFilters();
        });
    });

    // Handle age filter clicks
    ageFilterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all age buttons
            ageFilterButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            applyFilters();
        });
    });

    // Handle preference filter clicks
    preferenceFilterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all preference buttons
            preferenceFilterButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            applyFilters();
        });
    });

    // Handle direction filter clicks
    directionFilterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all direction buttons
            directionFilterButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            applyFilters();
        });
    });

    // Handle surface filter clicks
    surfaceFilterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all surface buttons
            surfaceFilterButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            applyFilters();
        });
    });

    // Handle condition filter clicks
    conditionFilterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all condition buttons
            conditionFilterButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            applyFilters();
        });
    });

    // Handle reset filters button
    document.getElementById('reset-filters').addEventListener('click', function() {
        resetAllFilters();
    });

    // Handle dark mode toggle
    const themeToggle = document.getElementById('theme-toggle');
    const body = document.body;

    // Check for saved theme preference or default to light mode
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);

    themeToggle.addEventListener('change', function() {
        const newTheme = this.checked ? 'dark' : 'light';
        setTheme(newTheme);
        localStorage.setItem('theme', newTheme);
    });

    function setTheme(theme) {
        if (theme === 'dark') {
            body.setAttribute('data-theme', 'dark');
            themeToggle.checked = true;
        } else {
            body.removeAttribute('data-theme');
            themeToggle.checked = false;
        }
    }

    // Handle search functionality
    const searchInput = document.getElementById('horse-search');
    const clearSearchBtn = document.getElementById('clear-search');
    let searchTerm = '';

    searchInput.addEventListener('input', function() {
        searchTerm = this.value.toLowerCase().trim();

        // Show/hide clear button
        if (searchTerm) {
            clearSearchBtn.classList.add('show');
        } else {
            clearSearchBtn.classList.remove('show');
        }

        applyFilters();
    });

    clearSearchBtn.addEventListener('click', function() {
        searchInput.value = '';
        searchTerm = '';
        clearSearchBtn.classList.remove('show');
        searchInput.focus();
        applyFilters();
    });

    // Add Enter key support for search
    searchInput.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            this.value = '';
            searchTerm = '';
            clearSearchBtn.classList.remove('show');
            applyFilters();
        }
    });

    function applyFilters() {
        const activeStatusFilter = document.querySelector('.status-filter.active').getAttribute('data-filter');
        const activeGenderFilter = document.querySelector('.gender-filter.active').getAttribute('data-filter');
        const activeAgeFilter = document.querySelector('.age-filter.active').getAttribute('data-filter');
        const activePreferenceFilter = document.querySelector('.preference-filter.active').getAttribute('data-filter');
        const activeDirectionFilter = document.querySelector('.direction-filter.active').getAttribute('data-filter');
        const activeSurfaceFilter = document.querySelector('.surface-filter.active').getAttribute('data-filter');
        const activeConditionFilter = document.querySelector('.condition-filter.active').getAttribute('data-filter');

        // Filter horse cards based on all filter categories
        horseCards.forEach(card => {
            let statusMatch = false;
            let genderMatch = false;
            let ageMatch = false;
            let preferenceMatch = false;
            let directionMatch = false;
            let surfaceMatch = false;
            let conditionMatch = false;
            let searchMatch = false;

            // Check status filter
            if (activeStatusFilter === 'all') {
                statusMatch = true;
            } else if (activeStatusFilter === 'foal') {
                statusMatch = card.getAttribute('data-status') === 'foal';
            } else if (activeStatusFilter === 'racing') {
                const status = card.getAttribute('data-status');
                statusMatch = status === 'racing' || status === 'ready to race' || status === 'exhausted';
            } else if (activeStatusFilter === 'retired') {
                statusMatch = card.getAttribute('data-status') === 'retired';
            } else if (activeStatusFilter === 'deceased') {
                statusMatch = card.getAttribute('data-status') === 'deceased';
            } else if (activeStatusFilter === 'pregnant') {
                statusMatch = card.getAttribute('data-status') === 'pregnant';
            }

            // Check gender filter
            if (activeGenderFilter === 'all-gender') {
                genderMatch = true;
            } else if (activeGenderFilter === 'mares') {
                const gender = card.getAttribute('data-gender');
                genderMatch = gender === 'filly' || gender === 'mare' || gender == 'female';
            } else if (activeGenderFilter === 'stallions') {
                const gender = card.getAttribute('data-gender');
                genderMatch = gender === 'colt' || gender === 'stallion' || gender == 'male';
            }

            // Check age filter
            if (activeAgeFilter === 'all-age') {
                ageMatch = true;
            } else {
                const age = parseInt(card.getAttribute('data-age'));
                if (activeAgeFilter === 'newborns') {
                    ageMatch = age === 0;
                } else if (activeAgeFilter === 'yearlings') {
                    ageMatch = age === 1;
                } else if (activeAgeFilter === 'juveniles') {
                    ageMatch = age === 2;
                } else if (activeAgeFilter === 'mature3') {
                    ageMatch = age === 3;
                } else if (activeAgeFilter === 'mature4-5') {
                    ageMatch = age >= 4 && age <= 5;
                } else if (activeAgeFilter === 'mature6-8') {
                    ageMatch = age >= 6 && age <= 8;
                }
            }

            // Check preference filter (star rating)
            if (activePreferenceFilter === 'all-preference') {
                preferenceMatch = true;
            } else {
                const totalStars = parseFloat(card.getAttribute('data-total-stars'));
                if (activePreferenceFilter === 'under5') {
                    preferenceMatch = totalStars < 5.0;
                } else if (activePreferenceFilter === 'mid-range') {
                    preferenceMatch = totalStars >= 5.5 && totalStars <= 7.5;
                } else if (activePreferenceFilter === 'high-range') {
                    preferenceMatch = totalStars >= 8.0 && totalStars <= 8.5;
                } else if (activePreferenceFilter === 'perfect') {
                    preferenceMatch = totalStars === 9.0;
                }
            }

            // Check direction filter
            if (activeDirectionFilter === 'all-direction') {
                directionMatch = true;
            } else {
                const direction = card.getAttribute('data-direction').toLowerCase();
                if (activeDirectionFilter === 'left') {
                    directionMatch = direction.includes('left');
                } else if (activeDirectionFilter === 'right') {
                    directionMatch = direction.includes('right');
                }
            }

            // Check surface filter
            if (activeSurfaceFilter === 'all-surface') {
                surfaceMatch = true;
            } else {
                const surface = card.getAttribute('data-surface').toLowerCase();
                if (activeSurfaceFilter === 'dirt') {
                    surfaceMatch = surface === 'dirt';
                } else if (activeSurfaceFilter === 'turf') {
                    surfaceMatch = surface === 'turf';
                }
            }

            // Check condition filter
            if (activeConditionFilter === 'all-condition') {
                conditionMatch = true;
            } else {
                const condition = card.getAttribute('data-condition').toLowerCase();
                if (activeConditionFilter === 'firm') {
                    conditionMatch = condition === 'firm' || condition === 'fast' || condition === 'good';
                } else if (activeConditionFilter === 'soft') {
                    conditionMatch = condition === 'soft' || condition === 'sloppy' || condition === 'yielding';
                }
            }

            // Check search filter
            if (!searchTerm) {
                searchMatch = true;
            } else {
                const horseName = card.querySelector('h2').textContent.toLowerCase();
                searchMatch = horseName.includes(searchTerm);
            }

            // Show card only if all filters match (including search)
            if (statusMatch && genderMatch && ageMatch && preferenceMatch && directionMatch && surfaceMatch && conditionMatch && searchMatch) {
                card.classList.remove('hidden');
            } else {
                card.classList.add('hidden');
            }
        });

        // Update button text with count
        updateFilterCounts();

        // Update filter summary count
        updateFilterSummary();
    }

    function updateFilterCounts() {
        const allButtons = [...statusFilterButtons, ...genderFilterButtons, ...ageFilterButtons, ...preferenceFilterButtons, ...directionFilterButtons, ...surfaceFilterButtons, ...conditionFilterButtons];

        allButtons.forEach(button => {
            const filter = button.getAttribute('data-filter');
            let count = 0;

            if (filter === 'all' || filter === 'all-gender' || filter === 'all-age' || filter === 'all-preference' || filter === 'all-direction' || filter === 'all-surface' || filter === 'all-condition') {
                count = horseCards.length;
            } else {
                horseCards.forEach(card => {
                    let matches = false;

                    // Status filters
                    if (filter === 'foal') {
                        matches = card.getAttribute('data-status') === 'foal';
                    } else if (filter === 'racing') {
                        const status = card.getAttribute('data-status');
                        matches = status === 'racing' || status === 'ready to race' || status === 'exhausted';
                    } else if (filter === 'retired') {
                        matches = card.getAttribute('data-status') === 'retired';
                    } else if (filter === 'deceased') {
                        matches = card.getAttribute('data-status') === 'deceased';
                    } else if (filter === 'pregnant') {
                        matches = card.getAttribute('data-status') === 'pregnant';
                    }
                    // Gender filters
                    else if (filter === 'mares') {
                        const gender = card.getAttribute('data-gender');
                        matches = gender === 'filly' || gender === 'mare' || gender === 'female';
                    } else if (filter === 'stallions') {
                        const gender = card.getAttribute('data-gender');
                        matches = gender === 'colt' || gender === 'stallion' || gender === 'male';
                    }
                    // Age filters
                    else if (filter === 'newborns') {
                        const age = parseInt(card.getAttribute('data-age'));
                        matches = age === 0;
                    } else if (filter === 'yearlings') {
                        const age = parseInt(card.getAttribute('data-age'));
                        matches = age === 1;
                    } else if (filter === 'juveniles') {
                        const age = parseInt(card.getAttribute('data-age'));
                        matches = age === 2;
                    } else if (filter === 'mature3') {
                        const age = parseInt(card.getAttribute('data-age'));
                        matches = age === 3;
                    } else if (filter === 'mature4-5') {
                        const age = parseInt(card.getAttribute('data-age'));
                        matches = age >= 4 && age <= 5;
                    } else if (filter === 'mature6-8') {
                        const age = parseInt(card.getAttribute('data-age'));
                        matches = age >= 6 && age <= 8;
                    }
                    // Preference filters
                    else if (filter === 'under5') {
                        const totalStars = parseFloat(card.getAttribute('data-total-stars'));
                        matches = totalStars < 5.0;
                    } else if (filter === 'mid-range') {
                        const totalStars = parseFloat(card.getAttribute('data-total-stars'));
                        matches = totalStars >= 5.5 && totalStars <= 7.5;
                    } else if (filter === 'high-range') {
                        const totalStars = parseFloat(card.getAttribute('data-total-stars'));
                        matches = totalStars >= 8.0 && totalStars <= 8.5;
                    } else if (filter === 'perfect') {
                        const totalStars = parseFloat(card.getAttribute('data-total-stars'));
                        matches = totalStars === 9.0;
                    }
                    // Direction filters
                    else if (filter === 'left') {
                        const direction = card.getAttribute('data-direction').toLowerCase();
                        matches = direction.includes('left');
                    } else if (filter === 'right') {
                        const direction = card.getAttribute('data-direction').toLowerCase();
                        matches = direction.includes('right');
                    }
                    // Surface filters
                    else if (filter === 'dirt') {
                        const surface = card.getAttribute('data-surface').toLowerCase();
                        matches = surface === 'dirt';
                    } else if (filter === 'turf') {
                        const surface = card.getAttribute('data-surface').toLowerCase();
                        matches = surface === 'turf';
                    }
                    // Condition filters
                    else if (filter === 'firm') {
                        const condition = card.getAttribute('data-condition').toLowerCase();
                        matches = condition === 'firm' || condition === 'fast' || condition === 'good';
                    } else if (filter === 'soft') {
                        const condition = card.getAttribute('data-condition').toLowerCase();
                        matches = condition === 'soft' || condition === 'sloppy' || condition === 'yielding';
                    }

                    if (matches) count++;
                });
            }

            // Update button text with count
            const originalText = button.textContent.split(' (')[0];
            button.textContent = `${originalText} (${count})`;
        });
    }

    function resetAllFilters() {
        // Remove active class from all filter buttons
        const allFilterButtons = [...statusFilterButtons, ...genderFilterButtons, ...ageFilterButtons, ...preferenceFilterButtons, ...directionFilterButtons, ...surfaceFilterButtons, ...conditionFilterButtons];
        allFilterButtons.forEach(btn => btn.classList.remove('active'));

        // Set all "All" buttons as active
        document.querySelector('[data-filter="all"]').classList.add('active');
        document.querySelector('[data-filter="all-gender"]').classList.add('active');
        document.querySelector('[data-filter="all-age"]').classList.add('active');
        document.querySelector('[data-filter="all-preference"]').classList.add('active');
        document.querySelector('[data-filter="all-direction"]').classList.add('active');
        document.querySelector('[data-filter="all-surface"]').classList.add('active');
        document.querySelector('[data-filter="all-condition"]').classList.add('active');

        // Clear search
        searchInput.value = '';
        searchTerm = '';
        clearSearchBtn.classList.remove('show');

        // Apply filters to show all horses
        applyFilters();
    }

    function updateFilterSummary() {
        const visibleCards = document.querySelectorAll('.horse-card:not(.hidden)');
        const totalCards = horseCards.length;
        const visibleCount = visibleCards.length;

        const filterCountElement = document.getElementById('filter-count');

        if (visibleCount === totalCards) {
            filterCountElement.textContent = `Showing all ${totalCards} horses`;
            filterCountElement.style.color = '#495057';
        } else {
            filterCountElement.textContent = `Showing ${visibleCount} of ${totalCards} horses`;
            filterCountElement.style.color = '#007bff';
        }
    }

    // Handle Update Stable button
    const updateStableBtn = document.getElementById('update-stable-btn');
    const btnText = updateStableBtn.querySelector('.btn-text');
    const spinner = updateStableBtn.querySelector('.spinner-border');
    const lastUpdateTime = document.getElementById('last-update-time');

    updateStableBtn.addEventListener('click', function() {
        // Disable button and show loading state
        updateStableBtn.disabled = true;
        btnText.textContent = 'Updating...';
        spinner.classList.remove('d-none');

        // Make AJAX request to update stable
        fetch('/update_stable', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update timestamp display
                lastUpdateTime.textContent = data.last_update;

                // Show success message briefly
                btnText.textContent = 'Updated!';
                setTimeout(() => {
                    btnText.textContent = 'Update Stable';
                }, 2000);

                // Reload the page to show fresh data
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                // Show error message
                btnText.textContent = 'Error!';
                alert('Error updating stable: ' + data.message);
                setTimeout(() => {
                    btnText.textContent = 'Update Stable';
                }, 3000);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            btnText.textContent = 'Error!';
            alert('Network error occurred while updating stable.');
            setTimeout(() => {
                btnText.textContent = 'Update Stable';
            }, 3000);
        })
        .finally(() => {
            // Re-enable button and hide spinner
            updateStableBtn.disabled = false;
            spinner.classList.add('d-none');
        });
    });

    // Initialize filters and counts
    applyFilters();
});
</script>

{% endblock %}
{% block update_stable %}
    <div class="d-flex align-items-center">
        <span class="last-update-text mr-3">
            <small class="text-muted">Last updated: <span id="last-update-time">{{ last_update }}</span></small>
        </span>
        <button type="button" class="btn btn-outline-primary" id="update-stable-btn">
            <span class="btn-text">Update Stable</span>
            <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
        </button>
    </div>
{% endblock %}
{% block stable_name %}
<h3>Vyperactive Genetics</h3>
{% endblock %}
{% block stable_id %}
<h8>Stable ID: 17678ff1-353d-4da5-85e7-01f5a2babef3</h8>
{% endblock %}