{% extends "base.html" %}
{% block content %}

<link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
<style>
.grid-container {
    display: flex;
    flex-wrap: wrap;
    overflow-y: auto;
    max-height: fit-content;
    max-width: fit-content;
    padding: 15px;
}

/* Filter button styles */
.filter-container {
    padding: 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 100;
}

.filter-btn {
    margin: 2px;
    border-radius: 20px;
    font-size: 14px;
    padding: 8px 16px;
    transition: all 0.2s ease;
}

.filter-btn.active {
    background-color: #007bff !important;
    border-color: #007bff !important;
    color: white !important;
}

.filter-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,5);
}

/* Hide filtered horses */
.horse-card.hidden {
    display: none !important;
}

/* Filter summary styles */
.filter-summary {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 10px 15px;
    text-align: center;
    position: sticky;
    top: 70px; /* Position below the filter container */
    z-index: 99;
}

.filter-count-text {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}
</style>

<!-- Filter Buttons -->
<div class="filter-container">
    <div class="d-flex flex-wrap align-items-center">
        <span class="mr-3 font-weight-bold">Status:</span>
        <button class="btn btn-outline-secondary filter-btn status-filter" data-filter="all">All</button>
        <button class="btn btn-outline-primary filter-btn status-filter" data-filter="foal">Foal</button>
        <button class="btn btn-outline-success filter-btn status-filter" data-filter="racing">Racing</button>
        <button class="btn btn-outline-warning filter-btn status-filter" data-filter="retired">Retired</button>
        <button class="btn btn-outline-dark filter-btn status-filter" data-filter="deceased">Deceased</button>
        <button class="btn btn-outline-info filter-btn status-filter" data-filter="pregnant">Pregnant</button>
        <span class="mx-3 text-muted">|</span>
        <span class="mr-2 font-weight-bold">Gender:</span>
        <button class="btn btn-outline-secondary filter-btn gender-filter" data-filter="all-gender">All</button>
        <button class="btn btn-outline-danger filter-btn gender-filter" data-filter="mares">Mares</button>
        <button class="btn btn-outline-primary filter-btn gender-filter" data-filter="stallions">Stallions</button>
        <span class="mx-3 text-muted">|</span>
        <span class="mr-2 font-weight-bold">Age:</span>
        <button class="btn btn-outline-secondary filter-btn age-filter" data-filter="all-age">All</button>
        <button class="btn btn-outline-info filter-btn age-filter" data-filter="newborns">Newborns</button>
        <button class="btn btn-outline-success filter-btn age-filter" data-filter="yearlings">Yearlings</button>
        <button class="btn btn-outline-warning filter-btn age-filter" data-filter="juveniles">Juveniles</button>
        <button class="btn btn-outline-primary filter-btn age-filter" data-filter="mature3">Mature 3</button>
        <button class="btn btn-outline-dark filter-btn age-filter" data-filter="mature4-5">Mature 4-5</button>
        <button class="btn btn-outline-danger filter-btn age-filter" data-filter="mature6-8">Mature 6-8</button>
        <span class="mx-3 text-muted">|</span>
        <span class="mr-2 font-weight-bold">Preference:</span>
        <button class="btn btn-outline-secondary filter-btn preference-filter" data-filter="all-preference">All</button>
        <button class="btn btn-outline-warning filter-btn preference-filter" data-filter="under5">&lt;5 Stars</button>
        <button class="btn btn-outline-info filter-btn preference-filter" data-filter="mid-range">5.5-7.5 Stars</button>
        <button class="btn btn-outline-success filter-btn preference-filter" data-filter="high-range">8-8.5 Stars</button>
        <button class="btn btn-outline-warning filter-btn preference-filter" data-filter="perfect">9 Stars</button>
    </div>
</div>

<!-- Filter Results Summary -->
<div class="filter-summary">
    <span id="filter-count" class="filter-count-text">Showing all horses</span>
</div>

<div class="grid-container">
{% for horse in horses %}
    {% include 'horse_card.html' %}
{% endfor %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const statusFilterButtons = document.querySelectorAll('.status-filter');
    const genderFilterButtons = document.querySelectorAll('.gender-filter');
    const ageFilterButtons = document.querySelectorAll('.age-filter');
    const preferenceFilterButtons = document.querySelectorAll('.preference-filter');
    const horseCards = document.querySelectorAll('.horse-card');

    // Set defaults as active
    document.querySelector('[data-filter="all"]').classList.add('active');
    document.querySelector('[data-filter="all-gender"]').classList.add('active');
    document.querySelector('[data-filter="all-age"]').classList.add('active');
    document.querySelector('[data-filter="all-preference"]').classList.add('active');

    // Handle status filter clicks
    statusFilterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all status buttons
            statusFilterButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            applyFilters();
        });
    });

    // Handle gender filter clicks
    genderFilterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all gender buttons
            genderFilterButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            applyFilters();
        });
    });

    // Handle age filter clicks
    ageFilterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all age buttons
            ageFilterButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            applyFilters();
        });
    });

    // Handle preference filter clicks
    preferenceFilterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all preference buttons
            preferenceFilterButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            applyFilters();
        });
    });

    function applyFilters() {
        const activeStatusFilter = document.querySelector('.status-filter.active').getAttribute('data-filter');
        const activeGenderFilter = document.querySelector('.gender-filter.active').getAttribute('data-filter');
        const activeAgeFilter = document.querySelector('.age-filter.active').getAttribute('data-filter');
        const activePreferenceFilter = document.querySelector('.preference-filter.active').getAttribute('data-filter');

        // Filter horse cards based on status, gender, age, and preference
        horseCards.forEach(card => {
            let statusMatch = false;
            let genderMatch = false;
            let ageMatch = false;
            let preferenceMatch = false;

            // Check status filter
            if (activeStatusFilter === 'all') {
                statusMatch = true;
            } else if (activeStatusFilter === 'foal') {
                statusMatch = card.getAttribute('data-status') === 'foal';
            } else if (activeStatusFilter === 'racing') {
                const status = card.getAttribute('data-status');
                statusMatch = status === 'racing' || status === 'ready to race' || status === 'exhausted';
            } else if (activeStatusFilter === 'retired') {
                statusMatch = card.getAttribute('data-status') === 'retired';
            } else if (activeStatusFilter === 'deceased') {
                statusMatch = card.getAttribute('data-status') === 'deceased';
            } else if (activeStatusFilter === 'pregnant') {
                statusMatch = card.getAttribute('data-status') === 'pregnant';
            }

            // Check gender filter
            if (activeGenderFilter === 'all-gender') {
                genderMatch = true;
            } else if (activeGenderFilter === 'mares') {
                const gender = card.getAttribute('data-gender');
                genderMatch = gender === 'filly' || gender === 'mare' || gender == 'female';
            } else if (activeGenderFilter === 'stallions') {
                const gender = card.getAttribute('data-gender');
                genderMatch = gender === 'colt' || gender === 'stallion' || gender == 'male';
            }

            // Check age filter
            if (activeAgeFilter === 'all-age') {
                ageMatch = true;
            } else {
                const age = parseInt(card.getAttribute('data-age'));
                if (activeAgeFilter === 'newborns') {
                    ageMatch = age === 0;
                } else if (activeAgeFilter === 'yearlings') {
                    ageMatch = age === 1;
                } else if (activeAgeFilter === 'juveniles') {
                    ageMatch = age === 2;
                } else if (activeAgeFilter === 'mature3') {
                    ageMatch = age === 3;
                } else if (activeAgeFilter === 'mature4-5') {
                    ageMatch = age >= 4 && age <= 5;
                } else if (activeAgeFilter === 'mature6-8') {
                    ageMatch = age >= 6 && age <= 8;
                }
            }

            // Check preference filter (star rating)
            if (activePreferenceFilter === 'all-preference') {
                preferenceMatch = true;
            } else {
                const totalStars = parseFloat(card.getAttribute('data-total-stars'));
                if (activePreferenceFilter === 'under5') {
                    preferenceMatch = totalStars < 5.0;
                } else if (activePreferenceFilter === 'mid-range') {
                    preferenceMatch = totalStars >= 5.5 && totalStars <= 7.5;
                } else if (activePreferenceFilter === 'high-range') {
                    preferenceMatch = totalStars >= 8.0 && totalStars <= 8.5;
                } else if (activePreferenceFilter === 'perfect') {
                    preferenceMatch = totalStars === 9.0;
                }
            }

            // Show card only if all filters match
            if (statusMatch && genderMatch && ageMatch && preferenceMatch) {
                card.classList.remove('hidden');
            } else {
                card.classList.add('hidden');
            }
        });

        // Update button text with count
        updateFilterCounts();

        // Update filter summary count
        updateFilterSummary();
    }

    function updateFilterCounts() {
        const allButtons = [...statusFilterButtons, ...genderFilterButtons, ...ageFilterButtons, ...preferenceFilterButtons];

        allButtons.forEach(button => {
            const filter = button.getAttribute('data-filter');
            let count = 0;

            if (filter === 'all' || filter === 'all-gender' || filter === 'all-age' || filter === 'all-preference') {
                count = horseCards.length;
            } else {
                horseCards.forEach(card => {
                    let matches = false;

                    // Status filters
                    if (filter === 'foal') {
                        matches = card.getAttribute('data-status') === 'foal';
                    } else if (filter === 'racing') {
                        const status = card.getAttribute('data-status');
                        matches = status === 'racing' || status === 'ready to race' || status === 'exhausted';
                    } else if (filter === 'retired') {
                        matches = card.getAttribute('data-status') === 'retired';
                    } else if (filter === 'deceased') {
                        matches = card.getAttribute('data-status') === 'deceased';
                    } else if (filter === 'pregnant') {
                        matches = card.getAttribute('data-status') === 'pregnant';
                    }
                    // Gender filters
                    else if (filter === 'mares') {
                        const gender = card.getAttribute('data-gender');
                        matches = gender === 'filly' || gender === 'mare' || gender === 'female';
                    } else if (filter === 'stallions') {
                        const gender = card.getAttribute('data-gender');
                        matches = gender === 'colt' || gender === 'stallion' || gender === 'male';
                    }
                    // Age filters
                    else if (filter === 'newborns') {
                        const age = parseInt(card.getAttribute('data-age'));
                        matches = age === 0;
                    } else if (filter === 'yearlings') {
                        const age = parseInt(card.getAttribute('data-age'));
                        matches = age === 1;
                    } else if (filter === 'juveniles') {
                        const age = parseInt(card.getAttribute('data-age'));
                        matches = age === 2;
                    } else if (filter === 'mature3') {
                        const age = parseInt(card.getAttribute('data-age'));
                        matches = age === 3;
                    } else if (filter === 'mature4-5') {
                        const age = parseInt(card.getAttribute('data-age'));
                        matches = age >= 4 && age <= 5;
                    } else if (filter === 'mature6-8') {
                        const age = parseInt(card.getAttribute('data-age'));
                        matches = age >= 6 && age <= 8;
                    }
                    // Preference filters
                    else if (filter === 'under5') {
                        const totalStars = parseFloat(card.getAttribute('data-total-stars'));
                        matches = totalStars < 5.0;
                    } else if (filter === 'mid-range') {
                        const totalStars = parseFloat(card.getAttribute('data-total-stars'));
                        matches = totalStars >= 5.5 && totalStars <= 7.5;
                    } else if (filter === 'high-range') {
                        const totalStars = parseFloat(card.getAttribute('data-total-stars'));
                        matches = totalStars >= 8.0 && totalStars <= 8.5;
                    } else if (filter === 'perfect') {
                        const totalStars = parseFloat(card.getAttribute('data-total-stars'));
                        matches = totalStars === 9.0;
                    }

                    if (matches) count++;
                });
            }

            // Update button text with count
            const originalText = button.textContent.split(' (')[0];
            button.textContent = `${originalText} (${count})`;
        });
    }

    function updateFilterSummary() {
        const visibleCards = document.querySelectorAll('.horse-card:not(.hidden)');
        const totalCards = horseCards.length;
        const visibleCount = visibleCards.length;

        const filterCountElement = document.getElementById('filter-count');

        if (visibleCount === totalCards) {
            filterCountElement.textContent = `Showing all ${totalCards} horses`;
            filterCountElement.style.color = '#495057';
        } else {
            filterCountElement.textContent = `Showing ${visibleCount} of ${totalCards} horses`;
            filterCountElement.style.color = '#007bff';
        }
    }

    // Initialize filters and counts
    applyFilters();
});
</script>

{% endblock %}
{% block update_stable %}
    <button type="button" class="btn btn-outline-primary">Update Stable</button>
{% endblock %}
{% block stable_name %}
<h3>Vyperactive Genetics</h3>
{% endblock %}
{% block stable_id %}
<h8>Stable ID: 17678ff1-353d-4da5-85e7-01f5a2babef3</h8>
{% endblock %}