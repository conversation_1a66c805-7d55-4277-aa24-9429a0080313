/* CSS Variables for theming */
:root {
    --bg-color: #ffffff;
    --text-color: #212529;
    --card-bg: #ffffff;
    --card-border: #ccc;
    --filter-bg: #f8f9fa;
    --filter-border: #dee2e6;
    --filter-text: #495057;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --gold-shadow: rgba(255, 215, 0, 0.3);
}

/* Dark mode variables */
[data-theme="dark"] {
    --bg-color: #1a1a1a;
    --text-color: #e9ecef;
    --card-bg: #2d2d2d;
    --card-border: #444;
    --filter-bg: #2d2d2d;
    --filter-border: #444;
    --filter-text: #e9ecef;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --gold-shadow: rgba(255, 215, 0, 0.5);
}

.horse-card {
  border: 1px solid var(--card-border, #ccc);
  border-radius: 10px;
  padding: 16px;
  width: 400px;
  margin: 5px;
  background: var(--card-bg, #ffffff);
  color: var(--text-color, #212529);
  display: inline-block;
  vertical-align: top;
  box-shadow: 0 4px 8px var(--shadow-color, rgba(0, 0, 0, 0.1));
  flex-direction: column;
  justify-content: space-between; /* pushes content to top and bottom */
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}
.horse-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.horse-image {
  width: 100px;
  height: auto;
}

/* Example border colors for different statuses */
.border-retired {
  border-color: #b35d7a; /* maroon/purple */
}
.border-exhausted {
  border-color: #b35d7a; /* same as retired, or pick another */
}
.border-foal {
  border-color: #22c55e; /* green */
}
.border-ready {
  border-color: #22c55e; /* green */
}
.horse-card-buttons {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-top: 5px;
  margin-bottom: 5px;
}

.horse-card-buttons button {
  width: 100%;
  padding: 5px 0;
  font-size: 1em;
  border: none;
  border-radius: 6px;
  background: #298195;
  cursor: pointer;
  transition: background 0.2s;
}

.horse-card-buttons button:hover {
  background: #e0e0e0;
}

/* Optional: Make the status bar also fill the card width */
.horse-status {
  width: 100%;
  box-sizing: border-box;
  margin: 10px 0 0 0;
  padding: 8px 0;
  text-align: center;
  border-radius: 8px;
  font-weight: bold;
}


.horse-status.retired { background: #5d6673; color: #fff; border-radius: 8px; }
.horse-status.ready { background: #42854e; color: #fff; border-radius: 8px; }
.horse-status.foal { background: #3873a5; color: #fff; border-radius: 8px; }
.horse-status.pregnant { background: #9a3a63; color: #fff; border-radius: 8px;}
.horse-status.deceased { background: #5a5f69; color: #fff; border-radius: 8px;}
.horse-status.exhausted { background: #b53e33; color: #fff; border-radius: 8px;}

/* Star rating styles */
.stars {
  color: #ffd700; /* Gold color for filled stars */
  font-size: 16px;
  font-weight: bold;
  margin-left: 4px;
}

.stat-item {
  margin-right: 15px;
  white-space: nowrap;
}

/* Gold border for 9-star horses */
.horse-card.gold-border {
  border: 3px solid #ffd700;
  box-shadow: 0 4px 8px var(--gold-shadow, rgba(255, 215, 0, 0.3)), inset 0 0 10px rgba(255, 215, 0, 0.2);
}

/* Breeding Card Styles */
.breeding-card {
    background: var(--card-bg, white);
    border: 3px solid #8B5CF6;
    border-radius: 12px;
    padding: 16px;
    margin: 16px;
    box-shadow: 0 4px 8px var(--shadow-color, rgba(0, 0, 0, 0.1));
    max-width: 400px;
    font-family: Arial, sans-serif;
    color: var(--text-color, #333);
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

.breeding-card.gold-border {
    border-color: #FFD700;
    box-shadow: 0 4px 12px var(--gold-shadow, rgba(255, 215, 0, 0.3));
}

.breeding-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.breeding-info {
    flex: 1;
}

.grade-badge {
    background-color: #8B5CF6;
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: bold;
    display: inline-block;
    margin-bottom: 8px;
}

.horse-name {
    font-size: 24px;
    font-weight: bold;
    margin: 0 0 8px 0;
    color: var(--text-color, #333);
}

.horse-details {
    font-size: 14px;
    color: #666;
}

.detail-separator {
    margin: 0 8px;
}

.horse-image-container {
    width: 80px;
    height: 80px;
    flex-shrink: 0;
}

.horse-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.preferences-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

[data-theme="dark"] .preferences-row {
    border-bottom-color: #444;
}

.preference-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.preference-label {
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 4px;
    color: var(--text-color, #333);
}

.preference-stars {
    font-size: 16px;
    color: #ffd700;
}

.racing-stats-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    font-size: 14px;
}

.stat-number {
    font-size: 18px;
    font-weight: bold;
    color: var(--text-color, #333);
}

.stat-record {
    color: #666;
}

.win-percentage {
    font-weight: bold;
    color: var(--text-color, #333);
}

.earnings {
    font-weight: bold;
    color: var(--text-color, #333);
}

.currency-icon {
    font-size: 16px;
}

.grade-badges-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    gap: 8px;
}

.grade-badge-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.grade-badge-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    color: white;
    margin-bottom: 4px;
}

.start-grade { background-color: #8B5CF6; }
.speed-grade { background-color: #8B5CF6; }
.stamina-grade { background-color: #10B981; }
.finish-grade { background-color: #8B5CF6; }
.heart-grade { background-color: #8B5CF6; }
.temper-grade { background-color: #10B981; }
.subgrade { background-color: #6B7280; }

.grade-label {
    font-size: 10px;
    color: #666;
    text-align: center;
}

.breeding-card-buttons {
    display: flex;
    gap: 8px;
}

.breeding-btn {
    flex: 1;
    padding: 8px 12px;
    font-size: 12px;
    border-radius: 6px;
}