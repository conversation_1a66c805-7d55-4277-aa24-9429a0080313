/* CSS Variables for theming */
:root {
    --bg-color: #ffffff;
    --text-color: #212529;
    --card-bg: #ffffff;
    --card-border: #ccc;
    --filter-bg: #f8f9fa;
    --filter-border: #dee2e6;
    --filter-text: #495057;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --gold-shadow: rgba(255, 215, 0, 0.3);
}

/* Dark mode variables */
[data-theme="dark"] {
    --bg-color: #1a1a1a;
    --text-color: #e9ecef;
    --card-bg: #2d2d2d;
    --card-border: #444;
    --filter-bg: #2d2d2d;
    --filter-border: #444;
    --filter-text: #e9ecef;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --gold-shadow: rgba(255, 215, 0, 0.5);
}

.horse-card {
  border: 1px solid var(--card-border, #ccc);
  border-radius: 10px;
  padding: 16px;
  width: 400px;
  margin: 5px;
  background: var(--card-bg, #ffffff);
  color: var(--text-color, #212529);
  display: inline-block;
  vertical-align: top;
  box-shadow: 0 4px 8px var(--shadow-color, rgba(0, 0, 0, 0.1));
  flex-direction: column;
  justify-content: space-between; /* pushes content to top and bottom */
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}
.horse-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.horse-image {
  width: 100px;
  height: auto;
}

/* Example border colors for different statuses */
.border-retired {
  border-color: #b35d7a; /* maroon/purple */
}
.border-exhausted {
  border-color: #b35d7a; /* same as retired, or pick another */
}
.border-foal {
  border-color: #22c55e; /* green */
}
.border-ready {
  border-color: #22c55e; /* green */
}
.horse-card-buttons {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-top: 5px;
  margin-bottom: 5px;
}

.horse-card-buttons button {
  width: 100%;
  padding: 5px 0;
  font-size: 1em;
  border: none;
  border-radius: 6px;
  background: #298195;
  cursor: pointer;
  transition: background 0.2s;
}

.horse-card-buttons button:hover {
  background: #e0e0e0;
}

/* Optional: Make the status bar also fill the card width */
.horse-status {
  width: 100%;
  box-sizing: border-box;
  margin: 10px 0 0 0;
  padding: 8px 0;
  text-align: center;
  border-radius: 8px;
  font-weight: bold;
}


.horse-status.retired { background: #5d6673; color: #fff; border-radius: 8px; }
.horse-status.ready { background: #42854e; color: #fff; border-radius: 8px; }
.horse-status.foal { background: #3873a5; color: #fff; border-radius: 8px; }
.horse-status.pregnant { background: #9a3a63; color: #fff; border-radius: 8px;}
.horse-status.deceased { background: #5a5f69; color: #fff; border-radius: 8px;}
.horse-status.exhausted { background: #b53e33; color: #fff; border-radius: 8px;}

/* Star rating styles */
.stars {
  color: #ffd700; /* Gold color for filled stars */
  font-size: 16px;
  font-weight: bold;
  margin-left: 4px;
}

.stat-item {
  margin-right: 15px;
  white-space: nowrap;
}

/* Gold border for 9-star horses */
.horse-card.gold-border {
  border: 3px solid #ffd700;
  box-shadow: 0 4px 8px var(--gold-shadow, rgba(255, 215, 0, 0.3)), inset 0 0 10px rgba(255, 215, 0, 0.2);
}