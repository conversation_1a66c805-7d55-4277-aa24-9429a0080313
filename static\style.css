.horse-card {
  border: 1px solid #ccc;
  border-radius: 10px;
  padding: 16px;
  width: 350px;
  margin: 5px;
  background: #e3e3e3;
  display: inline-block;
  vertical-align: top;
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* pushes content to top and bottom */
  height: 370px; /* or whatever fixed height you want */
}
.horse-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.horse-image {
  width: 100px;
  height: auto;
}

/* Example border colors for different statuses */
.border-retired {
  border-color: #b35d7a; /* maroon/purple */
}
.border-exhausted {
  border-color: #b35d7a; /* same as retired, or pick another */
}
.border-foal {
  border-color: #22c55e; /* green */
}
.border-ready {
  border-color: #22c55e; /* green */
}
.horse-card-buttons {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-top: 5px;
  margin-bottom: 5px;
}

.horse-card-buttons button {
  width: 100%;
  padding: 5px 0;
  font-size: 1em;
  border: none;
  border-radius: 6px;
  background: #298195;
  cursor: pointer;
  transition: background 0.2s;
}

.horse-card-buttons button:hover {
  background: #e0e0e0;
}

/* Optional: Make the status bar also fill the card width */
.horse-status {
  width: 100%;
  box-sizing: border-box;
  margin: 10px 0 0 0;
  padding: 8px 0;
  text-align: center;
  border-radius: 8px;
  font-weight: bold;
}


.horse-status.retired { background: #5d6673; color: #fff; border-radius: 8px; }
.horse-status.ready { background: #42854e; color: #fff; border-radius: 8px; }
.horse-status.foal { background: #3873a5; color: #fff; border-radius: 8px; }
.horse-status.pregnant { background: #9a3a63; color: #fff; border-radius: 8px;}
.horse-status.deceased { background: #5a5f69; color: #fff; border-radius: 8px;}
.horse-status.exhausted { background: #b53e33; color: #fff; border-radius: 8px;}